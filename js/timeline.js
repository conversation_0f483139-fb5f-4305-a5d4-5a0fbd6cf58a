// Timeline-specific functionality and animations
class TimelineController {
    constructor() {
        this.entries = [];
        this.currentEntry = 0;
        this.isAutoScrolling = false;
        this.scrollSpeed = 50;
        this.animationDelay = 200;
        this.observer = null;
        this.init();
    }

    init() {
        this.initIntersectionObserver();
        this.initSmoothScrolling();
        this.initKeyboardNavigation();
        this.initTouchGestures();
    }

    initIntersectionObserver() {
        // Enhanced intersection observer for timeline animations
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add staggered animation delay
                    setTimeout(() => {
                        entry.target.classList.add('animate');
                        this.animateTimelineEntry(entry.target);
                    }, index * this.animationDelay);
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        });
    }

    observeTimelineEntries() {
        const entries = document.querySelectorAll('.timeline-entry');
        entries.forEach(entry => {
            this.observer.observe(entry);
        });
        this.entries = Array.from(entries);
    }

    animateTimelineEntry(entry) {
        const content = entry.querySelector('.timeline-content');
        const image = entry.querySelector('.timeline-image');
        const marker = entry.querySelector('.timeline-marker');
        
        // Animate marker
        if (marker) {
            marker.style.animation = 'markerPulse 0.6s ease-out';
        }
        
        // Animate content with slide effect
        if (content) {
            const isOdd = Array.from(entry.parentNode.children).indexOf(entry) % 2 === 0;
            content.style.animation = `slideIn${isOdd ? 'Left' : 'Right'} 0.8s ease-out`;
        }
        
        // Animate image with zoom effect
        if (image) {
            image.style.animation = 'imageZoomIn 1s ease-out 0.3s both';
        }
        
        // Add sparkle effect to the entry
        this.addSparkleEffect(entry);
    }

    addSparkleEffect(entry) {
        const sparkles = ['✨', '💫', '⭐'];
        
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div');
                sparkle.innerHTML = sparkles[i % sparkles.length];
                sparkle.style.cssText = `
                    position: absolute;
                    font-size: 1.2rem;
                    pointer-events: none;
                    z-index: 10;
                    animation: sparkleEntry 2s ease-out forwards;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                `;
                
                entry.style.position = 'relative';
                entry.appendChild(sparkle);
                
                setTimeout(() => {
                    if (sparkle.parentNode) {
                        sparkle.parentNode.removeChild(sparkle);
                    }
                }, 2000);
            }, i * 300);
        }
    }

    initSmoothScrolling() {
        // Custom smooth scrolling for timeline navigation
        let isScrolling = false;
        
        window.addEventListener('wheel', (e) => {
            if (document.getElementById('timeline-page').classList.contains('active')) {
                e.preventDefault();
                
                if (!isScrolling) {
                    isScrolling = true;
                    
                    const delta = e.deltaY > 0 ? 1 : -1;
                    this.scrollToNextEntry(delta);
                    
                    setTimeout(() => {
                        isScrolling = false;
                    }, 800);
                }
            }
        }, { passive: false });
    }

    scrollToNextEntry(direction) {
        const targetEntry = this.currentEntry + direction;
        
        if (targetEntry >= 0 && targetEntry < this.entries.length) {
            this.currentEntry = targetEntry;
            this.scrollToEntry(this.currentEntry);
        } else if (targetEntry >= this.entries.length) {
            // Trigger proposal page transition
            this.triggerProposalTransition();
        }
    }

    scrollToEntry(index) {
        if (this.entries[index]) {
            const entry = this.entries[index];
            const offsetTop = entry.offsetTop - (window.innerHeight / 2) + (entry.offsetHeight / 2);
            
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            
            // Highlight current entry
            this.highlightCurrentEntry(index);
        }
    }

    highlightCurrentEntry(index) {
        // Remove previous highlights
        this.entries.forEach(entry => {
            entry.classList.remove('current-entry');
        });
        
        // Add highlight to current entry
        if (this.entries[index]) {
            this.entries[index].classList.add('current-entry');
        }
    }

    initKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (document.getElementById('timeline-page').classList.contains('active')) {
                switch(e.key) {
                    case 'ArrowDown':
                    case 'PageDown':
                        e.preventDefault();
                        this.scrollToNextEntry(1);
                        break;
                    case 'ArrowUp':
                    case 'PageUp':
                        e.preventDefault();
                        this.scrollToNextEntry(-1);
                        break;
                    case 'Home':
                        e.preventDefault();
                        this.currentEntry = 0;
                        this.scrollToEntry(0);
                        break;
                    case 'End':
                        e.preventDefault();
                        this.currentEntry = this.entries.length - 1;
                        this.scrollToEntry(this.currentEntry);
                        break;
                }
            }
        });
    }

    initTouchGestures() {
        let startY = 0;
        let endY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            endY = e.changedTouches[0].clientY;
            const deltaY = startY - endY;
            
            if (Math.abs(deltaY) > 50) { // Minimum swipe distance
                const direction = deltaY > 0 ? 1 : -1;
                this.scrollToNextEntry(direction);
            }
        });
    }

    triggerProposalTransition() {
        // Trigger the proposal page transition
        if (window.proposalWebsite) {
            window.proposalWebsite.transitionToProposal();
        }
    }

    // Auto-scroll functionality
    startAutoScroll() {
        if (this.isAutoScrolling) return;
        
        this.isAutoScrolling = true;
        const autoScrollInterval = setInterval(() => {
            if (this.currentEntry < this.entries.length - 1) {
                this.scrollToNextEntry(1);
            } else {
                clearInterval(autoScrollInterval);
                this.isAutoScrolling = false;
                this.triggerProposalTransition();
            }
        }, 3000); // 3 seconds per entry
    }

    stopAutoScroll() {
        this.isAutoScrolling = false;
    }

    // Image lazy loading and error handling
    initImageHandling() {
        const images = document.querySelectorAll('.timeline-image');
        
        images.forEach(img => {
            // Add loading placeholder
            img.style.background = 'linear-gradient(45deg, #f0f0f0, #e0e0e0)';
            img.style.backgroundSize = '200% 200%';
            img.style.animation = 'shimmer 1.5s ease-in-out infinite';
            
            // Handle image load
            img.addEventListener('load', () => {
                img.style.background = 'none';
                img.style.animation = 'none';
                img.classList.add('loaded');
            });
            
            // Handle image error
            img.addEventListener('error', () => {
                img.src = 'assets/images/placeholder.jpg';
                img.alt = 'Memory placeholder';
            });
        });
    }

    // Progress indicator
    createProgressIndicator() {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'timeline-progress';
        progressContainer.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">
                <span class="current">1</span> / <span class="total">${this.entries.length}</span>
            </div>
        `;
        
        document.getElementById('timeline-page').appendChild(progressContainer);
        
        // Update progress on scroll
        window.addEventListener('scroll', () => {
            this.updateProgress();
        });
    }

    updateProgress() {
        const progressFill = document.querySelector('.progress-fill');
        const currentSpan = document.querySelector('.progress-text .current');
        
        if (progressFill && currentSpan) {
            const progress = ((this.currentEntry + 1) / this.entries.length) * 100;
            progressFill.style.width = progress + '%';
            currentSpan.textContent = this.currentEntry + 1;
        }
    }

    // Cleanup method
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }
}

// Additional CSS for timeline animations
const timelineStyles = document.createElement('style');
timelineStyles.textContent = `
    @keyframes markerPulse {
        0% { transform: translateX(-50%) scale(1); }
        50% { transform: translateX(-50%) scale(1.3); box-shadow: 0 0 25px rgba(255, 215, 0, 0.8); }
        100% { transform: translateX(-50%) scale(1); }
    }
    
    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes imageZoomIn {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes sparkleEntry {
        0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
        }
        50% {
            opacity: 1;
            transform: scale(1.2) rotate(180deg);
        }
        100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
        }
    }
    
    @keyframes shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    .current-entry {
        transform: scale(1.02);
        z-index: 10;
    }
    
    .current-entry .timeline-content {
        box-shadow: 0 20px 60px rgba(255, 193, 204, 0.4);
        border-color: var(--accent-gold);
    }
    
    .timeline-image.loaded {
        animation: imageZoomIn 0.5s ease-out;
    }
    
    .timeline-progress {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        z-index: 100;
        font-family: 'Inter', sans-serif;
    }
    
    .progress-bar {
        width: 100px;
        height: 4px;
        background: #e0e0e0;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }
    
    .progress-fill {
        height: 100%;
        background: var(--accent-gold);
        transition: width 0.3s ease;
        border-radius: 2px;
    }
    
    .progress-text {
        text-align: center;
        font-size: 0.8rem;
        color: var(--text-dark);
    }
    
    @media (max-width: 768px) {
        .timeline-progress {
            top: 10px;
            right: 10px;
            padding: 0.5rem;
        }
        
        .progress-bar {
            width: 60px;
        }
    }
`;
document.head.appendChild(timelineStyles);

// Initialize timeline controller when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.timelineController = new TimelineController();
});

// Export for use in other files
window.TimelineController = TimelineController;
