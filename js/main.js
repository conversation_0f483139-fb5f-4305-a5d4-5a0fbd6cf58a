// Main application controller
class ProposalWebsite {
    constructor() {
        this.currentPage = 'landing-page';
        this.timelineData = null;
        this.isTransitioning = false;
        this.init();
    }

    async init() {
        try {
            // Load timeline data
            await this.loadTimelineData();
            
            // Initialize event listeners
            this.initEventListeners();
            
            // Initialize animations
            this.initAnimations();
            
            // Show landing page
            this.showPage('landing-page');
            
            console.log('Proposal website initialized successfully');
        } catch (error) {
            console.error('Error initializing website:', error);
        }
    }

    async loadTimelineData() {
        try {
            const response = await fetch('data/timeline-content.json');
            this.timelineData = await response.json();
        } catch (error) {
            console.error('Error loading timeline data:', error);
            // Fallback data if JSON fails to load
            this.timelineData = {
                timeline: [],
                settings: {
                    autoScrollSpeed: 50,
                    animationDelay: 200,
                    enableSoundEffects: true,
                    proposalTriggerPoint: 0.9
                }
            };
        }
    }

    initEventListeners() {
        // Landing page envelope click
        const envelope = document.getElementById('love-envelope');
        if (envelope) {
            envelope.addEventListener('click', () => {
                this.transitionToTimeline();
            });
        }

        // Proposal YES button
        const yesButton = document.getElementById('yes-button');
        if (yesButton) {
            yesButton.addEventListener('click', () => {
                this.handleProposalAccepted();
            });
        }

        // Scroll detection for timeline
        window.addEventListener('scroll', () => {
            this.handleTimelineScroll();
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                if (this.currentPage === 'landing-page') {
                    this.transitionToTimeline();
                } else if (this.currentPage === 'proposal-page') {
                    this.handleProposalAccepted();
                }
            }
        });

        // Prevent right-click context menu for a more immersive experience
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }

    initAnimations() {
        // Initialize floating hearts and sparkles
        this.createFloatingElements();
        
        // Start background animations
        this.startBackgroundAnimations();
    }

    showPage(pageId) {
        if (this.isTransitioning) return;
        
        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageId;
            
            // Page-specific initialization
            if (pageId === 'timeline-page') {
                this.initTimeline();
            } else if (pageId === 'proposal-page') {
                this.initProposal();
            }
        }
    }

    async transitionToTimeline() {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Add transition effect
        const landingPage = document.getElementById('landing-page');
        landingPage.style.transition = 'opacity 1s ease-out, transform 1s ease-out';
        landingPage.style.opacity = '0';
        landingPage.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            this.showPage('timeline-page');
            this.isTransitioning = false;
        }, 1000);
    }

    async transitionToProposal() {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Smooth transition to proposal
        const timelinePage = document.getElementById('timeline-page');
        timelinePage.style.transition = 'opacity 1.5s ease-out';
        timelinePage.style.opacity = '0';
        
        setTimeout(() => {
            this.showPage('proposal-page');
            this.isTransitioning = false;
        }, 1500);
    }

    initTimeline() {
        if (!this.timelineData || !this.timelineData.timeline) return;
        
        const timelineContainer = document.getElementById('timeline');
        if (!timelineContainer) return;
        
        // Clear existing content
        timelineContainer.innerHTML = '';
        
        // Create timeline entries
        this.timelineData.timeline.forEach((entry, index) => {
            const timelineEntry = this.createTimelineEntry(entry, index);
            timelineContainer.appendChild(timelineEntry);
        });
        
        // Initialize scroll animations
        this.initTimelineAnimations();
    }

    createTimelineEntry(entry, index) {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'timeline-entry';
        entryDiv.setAttribute('data-index', index);
        
        entryDiv.innerHTML = `
            <div class="timeline-marker"></div>
            <div class="timeline-content">
                <div class="timeline-date">${entry.date}</div>
                <img src="${entry.image}" alt="${entry.title}" class="timeline-image" 
                     onerror="this.src='assets/images/placeholder.jpg'">
                <div class="timeline-text">${entry.text}</div>
            </div>
        `;
        
        return entryDiv;
    }

    initTimelineAnimations() {
        // Animate timeline entries on scroll
        const entries = document.querySelectorAll('.timeline-entry');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, {
            threshold: 0.3,
            rootMargin: '0px 0px -100px 0px'
        });
        
        entries.forEach(entry => {
            observer.observe(entry);
        });
    }

    handleTimelineScroll() {
        if (this.currentPage !== 'timeline-page') return;
        
        const scrollPosition = window.scrollY;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercentage = scrollPosition / documentHeight;
        
        // Check if we should trigger the proposal
        const triggerPoint = this.timelineData?.settings?.proposalTriggerPoint || 0.9;
        
        if (scrollPercentage >= triggerPoint && !this.isTransitioning) {
            this.transitionToProposal();
        }
    }

    initProposal() {
        // Add extra sparkle effects
        this.createProposalEffects();
        
        // Auto-focus on YES button for accessibility
        setTimeout(() => {
            const yesButton = document.getElementById('yes-button');
            if (yesButton) {
                yesButton.focus();
            }
        }, 2000);
    }

    handleProposalAccepted() {
        const yesButton = document.getElementById('yes-button');
        const celebration = document.getElementById('celebration');
        
        // Button animation
        yesButton.style.transform = 'scale(1.2)';
        yesButton.style.background = '#ff69b4';
        yesButton.innerHTML = 'YES! I DO! 💕💍';
        
        // Create celebration effects
        this.createCelebrationEffects();
        
        // Show celebration message
        setTimeout(() => {
            this.showCelebrationMessage();
        }, 2000);
    }

    createFloatingElements() {
        // This will be enhanced by animations.js
        console.log('Creating floating elements...');
    }

    startBackgroundAnimations() {
        // This will be enhanced by animations.js
        console.log('Starting background animations...');
    }

    createProposalEffects() {
        // Enhanced proposal effects
        const proposalPage = document.getElementById('proposal-page');
        
        // Add extra floating hearts
        for (let i = 0; i < 10; i++) {
            setTimeout(() => {
                this.createFloatingHeart();
            }, i * 200);
        }
    }

    createFloatingHeart() {
        const heart = document.createElement('div');
        heart.innerHTML = '💕';
        heart.style.position = 'fixed';
        heart.style.fontSize = '2rem';
        heart.style.pointerEvents = 'none';
        heart.style.zIndex = '1000';
        heart.style.left = Math.random() * window.innerWidth + 'px';
        heart.style.top = window.innerHeight + 'px';
        heart.style.animation = 'floatUp 4s linear forwards';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
            heart.remove();
        }, 4000);
    }

    createCelebrationEffects() {
        // Confetti-like effect with hearts and sparkles
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                this.createConfettiPiece();
            }, i * 50);
        }
    }

    createConfettiPiece() {
        const symbols = ['💕', '💖', '💗', '💝', '✨', '🌟', '💍', '👰', '🤵'];
        const piece = document.createElement('div');
        piece.innerHTML = symbols[Math.floor(Math.random() * symbols.length)];
        piece.style.position = 'fixed';
        piece.style.fontSize = '1.5rem';
        piece.style.pointerEvents = 'none';
        piece.style.zIndex = '1001';
        piece.style.left = Math.random() * window.innerWidth + 'px';
        piece.style.top = '-50px';
        piece.style.animation = `confettiFall ${2 + Math.random() * 3}s linear forwards`;
        
        document.body.appendChild(piece);
        
        setTimeout(() => {
            piece.remove();
        }, 5000);
    }

    showCelebrationMessage() {
        const message = document.createElement('div');
        message.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                padding: 2rem;
                border-radius: 20px;
                text-align: center;
                font-family: 'Dancing Script', cursive;
                font-size: 2rem;
                color: #4a4a4a;
                box-shadow: 0 20px 60px rgba(255, 193, 204, 0.5);
                z-index: 1002;
                animation: fadeInScale 1s ease-out;
            ">
                🎉 Congratulations! 🎉<br>
                <span style="font-size: 1.2rem; font-family: 'Playfair Display', serif;">
                    You're engaged! 💍💕
                </span>
            </div>
        `;
        
        document.body.appendChild(message);
        
        // Remove message after 5 seconds
        setTimeout(() => {
            message.style.animation = 'fadeOut 1s ease-out forwards';
            setTimeout(() => message.remove(), 1000);
        }, 5000);
    }
}

// Initialize the website when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProposalWebsite();
});

// Add additional CSS animations for confetti
const style = document.createElement('style');
style.textContent = `
    @keyframes confettiFall {
        to {
            top: 100vh;
            transform: rotate(360deg);
        }
    }
    
    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
    
    @keyframes fadeOut {
        to {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
    }
`;
document.head.appendChild(style);
