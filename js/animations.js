// Advanced animations and visual effects
class AnimationController {
    constructor() {
        this.isAnimating = false;
        this.animationFrameId = null;
        this.particles = [];
        this.init();
    }

    init() {
        this.createParticleSystem();
        this.initAdvancedEffects();
        this.startAnimationLoop();
    }

    createParticleSystem() {
        // Create floating hearts for landing page
        this.createFloatingHearts();
        
        // Create sparkle effects
        this.createSparkleEffects();
        
        // Create romantic atmosphere
        this.createRomanticAtmosphere();
    }

    createFloatingHearts() {
        const heartsContainer = document.querySelector('.floating-hearts');
        if (!heartsContainer) return;

        const heartSymbols = ['💕', '💖', '💗', '💝', '❤️', '💘'];
        
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                const heart = document.createElement('div');
                heart.className = 'floating-heart';
                heart.innerHTML = heartSymbols[Math.floor(Math.random() * heartSymbols.length)];
                heart.style.cssText = `
                    position: absolute;
                    font-size: ${1 + Math.random() * 1.5}rem;
                    left: ${Math.random() * 100}%;
                    animation: floatUpContinuous ${8 + Math.random() * 4}s linear infinite;
                    animation-delay: ${Math.random() * 8}s;
                    opacity: ${0.4 + Math.random() * 0.4};
                    pointer-events: none;
                    z-index: 1;
                `;
                
                heartsContainer.appendChild(heart);
                
                // Remove heart after animation
                setTimeout(() => {
                    if (heart.parentNode) {
                        heart.parentNode.removeChild(heart);
                    }
                }, 12000);
            }, i * 1000);
        }
    }

    createSparkleEffects() {
        const sparklesContainer = document.querySelector('.sparkles');
        if (!sparklesContainer) return;

        const sparkleSymbols = ['✨', '⭐', '🌟', '💫', '⚡'];
        
        for (let i = 0; i < 6; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div');
                sparkle.className = 'floating-sparkle';
                sparkle.innerHTML = sparkleSymbols[Math.floor(Math.random() * sparkleSymbols.length)];
                sparkle.style.cssText = `
                    position: absolute;
                    font-size: ${0.8 + Math.random() * 1}rem;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: sparkleFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                    animation-delay: ${Math.random() * 3}s;
                    opacity: ${0.5 + Math.random() * 0.5};
                    pointer-events: none;
                    z-index: 1;
                `;
                
                sparklesContainer.appendChild(sparkle);
            }, i * 500);
        }
    }

    createRomanticAtmosphere() {
        // Add subtle moving gradients
        this.createMovingGradients();
        
        // Add gentle pulsing effects
        this.createPulsingEffects();
    }

    createMovingGradients() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
            
            .romantic-background::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, 
                    rgba(255, 193, 204, 0.1), 
                    rgba(255, 179, 186, 0.1), 
                    rgba(255, 209, 220, 0.1),
                    rgba(255, 193, 204, 0.1)
                );
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                pointer-events: none;
            }
        `;
        document.head.appendChild(style);
    }

    createPulsingEffects() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes gentlePulse {
                0%, 100% { opacity: 0.6; transform: scale(1); }
                50% { opacity: 0.8; transform: scale(1.05); }
            }
            
            .envelope:hover .heart-icon {
                animation: gentlePulse 1.5s ease-in-out infinite;
            }
        `;
        document.head.appendChild(style);
    }

    initAdvancedEffects() {
        // Add mouse follow effect for sparkles
        this.initMouseFollowEffect();
        
        // Add scroll-based animations
        this.initScrollAnimations();
        
        // Add touch effects for mobile
        this.initTouchEffects();
    }

    initMouseFollowEffect() {
        let mouseX = 0;
        let mouseY = 0;
        let sparkleTimeout;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            
            // Clear previous timeout
            clearTimeout(sparkleTimeout);
            
            // Create sparkle after a short delay
            sparkleTimeout = setTimeout(() => {
                this.createMouseSparkle(mouseX, mouseY);
            }, 100);
        });
    }

    createMouseSparkle(x, y) {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            font-size: 1rem;
            pointer-events: none;
            z-index: 1000;
            animation: sparkleDisappear 1s ease-out forwards;
            transform: translate(-50%, -50%);
        `;
        
        document.body.appendChild(sparkle);
        
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 1000);
    }

    initScrollAnimations() {
        // Parallax effect for background elements
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.letter-graphic');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.2);
                element.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
            });
        });
    }

    initTouchEffects() {
        // Add touch ripple effects
        document.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            this.createTouchRipple(touch.clientX, touch.clientY);
        });
    }

    createTouchRipple(x, y) {
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 193, 204, 0.6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: rippleExpand 0.6s ease-out forwards;
            transform: translate(-50%, -50%);
        `;
        
        document.body.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    startAnimationLoop() {
        const animate = () => {
            // Continuous floating hearts
            if (Math.random() < 0.02) {
                this.createFloatingHearts();
            }
            
            // Continuous sparkles
            if (Math.random() < 0.01) {
                this.createSparkleEffects();
            }
            
            this.animationFrameId = requestAnimationFrame(animate);
        };
        
        animate();
    }

    // Enhanced proposal page animations
    createProposalFireworks() {
        const colors = ['#ff69b4', '#ffd700', '#ff1493', '#ffc0cb', '#ff6347'];
        
        for (let i = 0; i < 20; i++) {
            setTimeout(() => {
                this.createFireworkBurst(
                    Math.random() * window.innerWidth,
                    Math.random() * window.innerHeight * 0.7,
                    colors[Math.floor(Math.random() * colors.length)]
                );
            }, i * 200);
        }
    }

    createFireworkBurst(x, y, color) {
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            const angle = (i / particleCount) * Math.PI * 2;
            const velocity = 50 + Math.random() * 50;
            
            particle.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                width: 4px;
                height: 4px;
                background: ${color};
                border-radius: 50%;
                pointer-events: none;
                z-index: 1001;
            `;
            
            document.body.appendChild(particle);
            
            // Animate particle
            let currentX = x;
            let currentY = y;
            let velocityX = Math.cos(angle) * velocity;
            let velocityY = Math.sin(angle) * velocity;
            let opacity = 1;
            
            const animateParticle = () => {
                currentX += velocityX * 0.02;
                currentY += velocityY * 0.02;
                velocityY += 0.5; // gravity
                opacity -= 0.02;
                
                particle.style.left = currentX + 'px';
                particle.style.top = currentY + 'px';
                particle.style.opacity = opacity;
                
                if (opacity > 0) {
                    requestAnimationFrame(animateParticle);
                } else {
                    particle.remove();
                }
            };
            
            animateParticle();
        }
    }

    // Cleanup method
    destroy() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
    }
}

// Additional CSS animations
const animationStyles = document.createElement('style');
animationStyles.textContent = `
    @keyframes floatUpContinuous {
        0% {
            bottom: -50px;
            opacity: 0;
            transform: translateX(0) rotate(0deg);
        }
        10% {
            opacity: 0.8;
        }
        90% {
            opacity: 0.8;
        }
        100% {
            bottom: 100vh;
            opacity: 0;
            transform: translateX(20px) rotate(360deg);
        }
    }
    
    @keyframes sparkleFloat {
        0%, 100% {
            transform: translateY(0) rotate(0deg) scale(1);
            opacity: 0.5;
        }
        25% {
            transform: translateY(-10px) rotate(90deg) scale(1.2);
            opacity: 1;
        }
        50% {
            transform: translateY(0) rotate(180deg) scale(0.8);
            opacity: 0.7;
        }
        75% {
            transform: translateY(-5px) rotate(270deg) scale(1.1);
            opacity: 1;
        }
    }
    
    @keyframes sparkleDisappear {
        0% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(2) rotate(180deg);
        }
    }
    
    @keyframes rippleExpand {
        0% {
            width: 20px;
            height: 20px;
            opacity: 1;
        }
        100% {
            width: 100px;
            height: 100px;
            opacity: 0;
        }
    }
    
    .floating-heart {
        will-change: transform, opacity;
    }
    
    .floating-sparkle {
        will-change: transform, opacity;
    }
`;
document.head.appendChild(animationStyles);

// Initialize animation controller when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.animationController = new AnimationController();
});

// Export for use in main.js
window.AnimationController = AnimationController;
