# 💕 Romantic Proposal Website

A beautiful, interactive website designed for marriage proposals, featuring a romantic timeline journey and an elegant proposal page.

## ✨ Features

### Page 1 - Landing Page
- Centered love-themed envelope button with hover animations
- Romantic decorative elements (floating hearts, sparkles, letter graphics)
- Soft pink and gold color scheme
- Responsive design for all devices
- Interactive hover effects and smooth animations

### Page 2 - Timeline Journey
- Vertical timeline layout with photo gallery
- Smooth scrolling between timeline entries
- Animated entry reveals as you scroll
- Responsive design that adapts to mobile
- Progress indicator showing journey completion
- Keyboard and touch navigation support

### Page 3 - Proposal Page
- Elegant "Will you Marry Me?" typography
- Prominent "YES!" button (no way to say no! 😉)
- Romantic visual effects (floating hearts, sparkles, fireworks)
- Celebration animation when "YES" is clicked
- Smooth transition from timeline

## 🚀 Getting Started

### Quick Setup
1. Clone or download this repository
2. Replace placeholder images in `assets/images/` with your own photos
3. Customize the timeline content in `data/timeline-content.json`
4. Open `index.html` in a web browser
5. Share the magic! 💍

### File Structure
```
proposal-website/
├── index.html              # Main HTML file
├── css/
│   └── styles.css          # All styling and animations
├── js/
│   ├── main.js            # Main application logic
│   ├── animations.js      # Advanced animations and effects
│   └── timeline.js        # Timeline-specific functionality
├── data/
│   └── timeline-content.json  # Timeline content configuration
├── assets/
│   └── images/            # Your romantic photos
└── README.md              # This documentation
```

## 🎨 Customization Guide

### Adding Your Photos
1. Navigate to `assets/images/`
2. Add your photos with these recommended names:
   - `first-meeting.jpg`
   - `first-valentine.jpg`
   - `summer-adventure.jpg`
   - `moving-in.jpg`
   - `first-christmas.jpg`
   - `meeting-family.jpg`
   - `first-trip.jpg`
   - `first-pet.jpg`
   - `second-valentine.jpg`
   - `ring-shopping.jpg`
   - `planning-proposal.jpg`
   - `proposal-moment.jpg`

**Image Requirements:**
- Recommended size: 400x300 pixels
- Formats: JPG, PNG, WebP
- Keep file sizes under 500KB for fast loading

### Customizing Timeline Content
Edit `data/timeline-content.json` to personalize your story:

```json
{
  "timeline": [
    {
      "id": 1,
      "date": "Your Special Date",
      "title": "Your Memory Title",
      "text": "Your romantic story for this moment...",
      "image": "assets/images/your-photo.jpg"
    }
  ]
}
```

### Styling Customization
Edit `css/styles.css` to change:
- **Colors**: Modify CSS variables in `:root`
- **Fonts**: Change font families in the CSS
- **Animations**: Adjust animation durations and effects
- **Layout**: Modify spacing and sizing

### Color Scheme Variables
```css
:root {
    --primary-pink: #ffc1cc;      /* Main pink color */
    --secondary-pink: #ffb3ba;    /* Secondary pink */
    --accent-gold: #ffd700;       /* Gold accents */
    --soft-white: #fefefe;        /* Background white */
    --text-dark: #4a4a4a;         /* Text color */
}
```

## 🎯 Advanced Features

### Auto-Scroll Timeline
Enable automatic timeline progression:
```javascript
// In main.js, add this to timeline initialization
timelineController.startAutoScroll();
```

### Sound Effects
Add background music or sound effects:
1. Add audio files to `assets/sounds/`
2. Modify `js/main.js` to include audio playback
3. Set `enableSoundEffects: true` in timeline-content.json

### Custom Animations
Add your own animations by:
1. Creating new CSS keyframes in `styles.css`
2. Adding animation triggers in `animations.js`
3. Customizing timing in the settings

## 📱 Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## 🎭 Performance Tips

1. **Optimize Images**: Compress photos before uploading
2. **Preload**: Critical images are preloaded for smooth experience
3. **Lazy Loading**: Timeline images load as needed
4. **Smooth Animations**: Hardware-accelerated CSS animations

## 🛠️ Troubleshooting

### Images Not Loading
- Check file paths in `timeline-content.json`
- Ensure images are in `assets/images/` directory
- Verify image file extensions match JSON references

### Animations Not Working
- Ensure JavaScript is enabled in browser
- Check browser console for errors
- Verify all JS files are properly linked

### Mobile Issues
- Test on actual devices, not just browser dev tools
- Ensure touch events are working
- Check viewport meta tag is present

## 💡 Tips for the Perfect Proposal

1. **Test Everything**: Run through the entire experience beforehand
2. **Backup Plan**: Have photos saved locally in case of internet issues
3. **Timing**: Choose a moment when you won't be interrupted
4. **Environment**: Ensure good lighting for viewing the screen
5. **Practice**: Know how to navigate if needed

## 🎉 After the "YES!"

The website includes a celebration animation, but don't forget to:
- Have the real ring ready! 💍
- Capture the moment with photos/video
- Share your joy with family and friends
- Start planning your wedding! 👰🤵

## 📞 Support

If you need help customizing or have questions:
1. Check this README first
2. Look at the code comments for guidance
3. Test changes in small steps
4. Keep backups of working versions

## 💝 Final Notes

This website is designed to create a magical moment. The most important thing is the love you share, not the technology. The website is just a beautiful way to express what's already in your heart.

**Good luck with your proposal! 💕**

---

*Made with ❤️ for love stories everywhere*
