# 🎨 Quick Customization Template

Use this template to quickly personalize your proposal website.

## 📝 Step 1: Update Timeline Content

Copy this template and fill in your own story in `data/timeline-content.json`:

```json
{
  "timeline": [
    {
      "id": 1,
      "date": "Month Day, Year",
      "title": "The Day We Met",
      "text": "Write your story about how you first met...",
      "image": "assets/images/first-meeting.jpg"
    },
    {
      "id": 2,
      "date": "Month Day, Year", 
      "title": "Our First Date",
      "text": "Describe your magical first date...",
      "image": "assets/images/first-date.jpg"
    },
    {
      "id": 3,
      "date": "Month Day, Year",
      "title": "Becoming Official",
      "text": "Tell the story of when you became a couple...",
      "image": "assets/images/official.jpg"
    },
    {
      "id": 4,
      "date": "Month Day, Year",
      "title": "Moving In Together",
      "text": "Share the excitement of your first home together...",
      "image": "assets/images/moving-in.jpg"
    },
    {
      "id": 5,
      "date": "Month Day, Year",
      "title": "Meeting the Family",
      "text": "Describe meeting each other's families...",
      "image": "assets/images/family.jpg"
    },
    {
      "id": 6,
      "date": "Month Day, Year",
      "title": "Our First Trip",
      "text": "Remember your first vacation together...",
      "image": "assets/images/trip.jpg"
    },
    {
      "id": 7,
      "date": "Month Day, Year",
      "title": "A Special Milestone",
      "text": "Share another important moment in your relationship...",
      "image": "assets/images/milestone.jpg"
    },
    {
      "id": 8,
      "date": "Today",
      "title": "The Question",
      "text": "And now, here we are. After all these beautiful moments...",
      "image": "assets/images/proposal.jpg"
    }
  ]
}
```

## 🖼️ Step 2: Prepare Your Photos

Create these image files in the `assets/images/` folder:

- [ ] `first-meeting.jpg` - Photo from when you first met
- [ ] `first-date.jpg` - Picture from your first date
- [ ] `official.jpg` - When you became official
- [ ] `moving-in.jpg` - Moving in together
- [ ] `family.jpg` - Meeting families
- [ ] `trip.jpg` - Your first trip
- [ ] `milestone.jpg` - Another special moment
- [ ] `proposal.jpg` - A recent photo of you both

**Photo Tips:**
- Size: 400x300 pixels (or similar ratio)
- Format: JPG or PNG
- Keep under 500KB each
- Choose photos that tell your story

## 🎨 Step 3: Customize Colors (Optional)

To change the color scheme, edit these variables in `css/styles.css`:

```css
:root {
    --primary-pink: #ffc1cc;      /* Main pink - change to your favorite color */
    --secondary-pink: #ffb3ba;    /* Secondary pink */
    --accent-gold: #ffd700;       /* Gold accents - try #ff69b4 for hot pink */
    --soft-white: #fefefe;        /* Background */
    --text-dark: #4a4a4a;         /* Text color */
}
```

**Popular Color Combinations:**
- **Classic Romance**: Keep default pink and gold
- **Elegant**: `#e6e6fa` (lavender) and `#dda0dd` (plum)
- **Modern**: `#ff6b6b` (coral) and `#4ecdc4` (teal)
- **Vintage**: `#d4a574` (tan) and `#8b4513` (brown)

## ✏️ Step 4: Personalize Text

Update these text elements in `index.html`:

1. **Main Title** (line ~18):
   ```html
   <h1 class="main-title">Our Love Story</h1>
   ```
   Change to: "Sarah & John's Journey" or your names

2. **Subtitle** (line ~19):
   ```html
   <p class="subtitle">A journey of two hearts becoming one</p>
   ```
   Change to your own romantic phrase

3. **Envelope Text** (line ~26):
   ```html
   <p class="envelope-text">Click to open our story</p>
   ```
   Change to: "Click to see our journey" or similar

4. **Timeline Title** (line ~35):
   ```html
   <h2 class="timeline-title">Our Journey Together</h2>
   ```
   Change to: "Our Love Story" or "How We Fell in Love"

## 🎵 Step 5: Add Music (Optional)

To add background music:

1. Add an audio file to `assets/sounds/background.mp3`
2. Add this to the `<head>` section of `index.html`:
   ```html
   <audio id="background-music" loop>
       <source src="assets/sounds/background.mp3" type="audio/mpeg">
   </audio>
   ```
3. Add this JavaScript to `js/main.js` in the `init()` function:
   ```javascript
   const music = document.getElementById('background-music');
   if (music) {
       music.volume = 0.3; // Adjust volume (0.0 to 1.0)
       music.play().catch(e => console.log('Music autoplay blocked'));
   }
   ```

## 🧪 Step 6: Test Your Website

1. Open `index.html` in your web browser
2. Click through all three pages
3. Check that all images load correctly
4. Test on mobile device
5. Make sure the timeline scrolls smoothly
6. Verify the proposal page works

## 📋 Final Checklist

Before the big moment:

- [ ] All photos are uploaded and display correctly
- [ ] Timeline content tells your unique story
- [ ] Colors match your preferences
- [ ] Text is personalized
- [ ] Website works on mobile
- [ ] You've practiced navigating the site
- [ ] You have the real ring ready! 💍

## 🆘 Quick Fixes

**Images not showing?**
- Check file names match exactly (case-sensitive)
- Ensure images are in `assets/images/` folder

**Text looks weird?**
- Check for missing quotes in JSON
- Validate JSON at jsonlint.com

**Colors not changing?**
- Clear browser cache (Ctrl+F5 or Cmd+Shift+R)
- Check CSS syntax

**Site not working on mobile?**
- Test on actual device, not just browser tools
- Check that files are uploaded to web server if hosting online

---

**You're ready to create magic! 💕✨**
